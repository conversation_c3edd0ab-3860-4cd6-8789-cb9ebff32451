"use client";

import { useState, useEffect, useCallback } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { FileText, Download } from "lucide-react";
import { toast } from "sonner";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";

interface Template {
  id: string;
  name: string;
  description: string;
  filename: string;
  placeholders: string[];
  layoutSize: "A4" | "Letter";
  uploadedAt: string;
}

export default function TemplateFormPage() {
  const params = useParams();
  const router = useRouter();
  const [template, setTemplate] = useState<Template | null>(null);
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);

  const loadTemplate = useCallback(
    async (id: string) => {
      try {
        const response = await fetch("/api/templates");
        if (!response.ok) {
          throw new Error("Failed to load templates");
        }
        const templates = await response.json();
        const foundTemplate = templates.find((t: Template) => t.id === id);

        if (!foundTemplate) {
          toast.error("Template not found");
          router.push("/");
          return;
        }

        setTemplate(foundTemplate);

        // Initialize form data with empty values for all placeholders
        const initialData: Record<string, string> = {};
        foundTemplate.placeholders.forEach((placeholder: string) => {
          const key = placeholder.replace(/[\[\]]/g, ""); // Remove brackets
          initialData[key] = "";
        });
        setFormData(initialData);
      } catch (error) {
        console.error("Error loading template:", error);
        toast.error("Failed to load template");
        router.push("/");
      } finally {
        setIsLoading(false);
      }
    },
    [router]
  );

  useEffect(() => {
    if (params.id) {
      loadTemplate(params.id as string);
    }
  }, [params.id, loadTemplate]);

  const handleInputChange = (key: string, value: string) => {
    setFormData((prev) => ({ ...prev, [key]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!template) return;

    // Validate that all fields are filled
    const emptyFields = Object.entries(formData).filter(
      ([, value]) => !value.trim()
    );
    if (emptyFields.length > 0) {
      toast.error("Please fill in all fields");
      return;
    }

    setIsGenerating(true);

    try {
      // Generate the document by calling the API
      const response = await fetch("/api/templates/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          templateId: template.id,
          data: formData,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to generate document");
      }

      const result = await response.json();

      // Create a temporary div to render the HTML content
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = result.htmlContent;
      tempDiv.style.position = "absolute";
      tempDiv.style.left = "-9999px";
      tempDiv.style.top = "-9999px";
      tempDiv.style.width = result.layoutSize === "A4" ? "210mm" : "8.5in";
      tempDiv.style.minWidth = result.layoutSize === "A4" ? "210mm" : "8.5in";
      tempDiv.style.backgroundColor = "white";
      tempDiv.style.fontFamily = "'Times New Roman', serif";
      tempDiv.style.fontSize = "12pt";
      tempDiv.style.lineHeight = "1.15";
      tempDiv.style.color = "black";
      tempDiv.style.padding = "0";
      tempDiv.style.boxSizing = "border-box";
      tempDiv.style.overflow = "visible";
      document.body.appendChild(tempDiv);

      try {
        // Wait for fonts and layout to load
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Get the actual content dimensions including overflow
        const actualWidth = Math.max(tempDiv.scrollWidth, tempDiv.offsetWidth);
        const actualHeight = Math.max(
          tempDiv.scrollHeight,
          tempDiv.offsetHeight
        );

        // Convert HTML to canvas with better settings for Word documents
        const canvas = await html2canvas(tempDiv, {
          scale: 2,
          useCORS: true,
          allowTaint: true,
          backgroundColor: "#ffffff",
          width: actualWidth,
          height: actualHeight,
          scrollX: 0,
          scrollY: 0,
          windowWidth: actualWidth,
          windowHeight: actualHeight,
          ignoreElements: () => false,
          removeContainer: true,
        });

        // Create PDF with appropriate dimensions
        const imgData = canvas.toDataURL("image/png");
        const pdf = new jsPDF({
          orientation: "portrait",
          unit: "mm",
          format: result.layoutSize === "A4" ? "a4" : "letter",
        });

        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = pdf.internal.pageSize.getHeight();

        // Convert canvas dimensions to mm (assuming 96 DPI)
        const imgWidthMM = (canvas.width * 25.4) / 96;
        const imgHeightMM = (canvas.height * 25.4) / 96;

        // Scale the image to fit the page size (content already includes margins)
        const widthRatio = pdfWidth / imgWidthMM;
        const heightRatio = pdfHeight / imgHeightMM;
        const scale = Math.min(widthRatio, heightRatio);

        const scaledWidth = imgWidthMM * scale;
        const scaledHeight = imgHeightMM * scale;

        // Center the image on the page
        const imgX = (pdfWidth - scaledWidth) / 2;
        const imgY = (pdfHeight - scaledHeight) / 2;

        pdf.addImage(imgData, "PNG", imgX, imgY, scaledWidth, scaledHeight);

        // Download the PDF
        const filename = `${result.templateName
          .replace(/[^a-z0-9]/gi, "_")
          .toLowerCase()}_${result.layoutSize.toLowerCase()}.pdf`;
        pdf.save(filename);

        toast.success(
          `PDF document generated and downloaded successfully! (${result.layoutSize} format)`
        );
      } finally {
        // Clean up the temporary div
        document.body.removeChild(tempDiv);
      }
    } catch (error) {
      console.error("Error generating document:", error);
      toast.error("Failed to generate document");
    } finally {
      setIsGenerating(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading template...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!template) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <div className="mb-8">
        <h1 className="text-3xl font-semibold text-foreground mb-2">
          {template.name}
        </h1>
        <p className="text-muted-foreground">{template.description}</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Document Information
          </CardTitle>
          <CardDescription>
            Fill in the required information to generate your document.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {template.placeholders.map((placeholder) => {
              const key = placeholder.replace(/[\[\]]/g, ""); // Remove brackets for form key
              const label = key.charAt(0).toUpperCase() + key.slice(1); // Capitalize first letter

              return (
                <div key={placeholder} className="space-y-2">
                  <Label htmlFor={key}>{label}</Label>
                  <Input
                    id={key}
                    type="text"
                    placeholder={`Enter ${label.toLowerCase()}`}
                    value={formData[key] || ""}
                    onChange={(e) => handleInputChange(key, e.target.value)}
                    required
                  />
                </div>
              );
            })}

            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                disabled={isGenerating}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isGenerating} className="flex-1">
                {isGenerating ? (
                  "Generating..."
                ) : (
                  <>
                    <Download className="h-4 w-4" />
                    Generate Document
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
